-- ###########################################################################
-- 会计师事务所SaaS系统 - 无外键+数据权限字段版SQL（MySQL 8.0+）
-- 调整点：1. 移除所有外键约束 2. 新增数据权限字段 3. 保留实体关联字段
-- ###########################################################################

-- 1. 创建数据库（若未创建）
CREATE DATABASE IF NOT EXISTS accounting_saas 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_unicode_ci;

USE accounting_saas;
SET default_storage_engine = INNODB;

-- ###########################################################################
-- 一、平台基础模块（角色/功能权限/平台管理员）
-- ###########################################################################

-- 1.1 角色表（saas_role）- 新增data_scope（角色数据范围）
CREATE TABLE `saas_role` (
  `role_id` INT NOT NULL AUTO_INCREMENT COMMENT '角色唯一ID',
  `role_name` VARCHAR(30) NOT NULL COMMENT '角色名称（唯一）：super_admin、tenant_admin',
  `role_type` VARCHAR(20) NOT NULL COMMENT '角色层级：platform_level/tenant_level',
  `role_description` VARCHAR(200) DEFAULT NULL COMMENT '角色描述',
  `data_scope` VARCHAR(20) NOT NULL COMMENT '角色默认数据范围：platform/tenant/customer',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`role_id`),
  UNIQUE KEY `uk_role_name` (`role_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表（RBAC核心）';

-- 1.2 功能权限表（saas_permission）- 新增data_perm_flag（是否关联数据权限）
CREATE TABLE `saas_permission` (
  `permission_id` INT NOT NULL AUTO_INCREMENT COMMENT '权限唯一ID',
  `permission_name` VARCHAR(50) NOT NULL COMMENT '权限名称（唯一）',
  `permission_desc` VARCHAR(200) DEFAULT NULL COMMENT '权限描述',
  `permission_type` VARCHAR(20) NOT NULL COMMENT 'system/business',
  `data_perm_flag` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否需数据权限：0否1是',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`permission_id`),
  UNIQUE KEY `uk_permission_name` (`permission_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='功能权限表（RBAC核心）';

-- 1.3 角色-功能权限关联表（saas_role_permission）- 保留role_id/permission_id关联
CREATE TABLE `saas_role_permission` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '关联记录唯一ID',
  `role_id` INT NOT NULL COMMENT '关联saas_role.role_id',
  `permission_id` INT NOT NULL COMMENT '关联saas_permission.permission_id',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_perm` (`role_id`,`permission_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_permission_id` (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色-功能权限关联表';

-- 1.4 平台管理员表（saas_platform_admin）- 新增data_scope（管理员数据范围）
CREATE TABLE `saas_platform_admin` (
  `admin_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '管理员唯一ID',
  `admin_account` VARCHAR(50) NOT NULL COMMENT '登录账号（唯一）',
  `login_password` VARCHAR(64) NOT NULL COMMENT '加密密码（SHA256+盐值）',
  `admin_name` VARCHAR(30) NOT NULL COMMENT '管理员姓名',
  `admin_role_id` INT NOT NULL COMMENT '关联saas_role.role_id（平台级角色）',
  `data_scope` VARCHAR(20) NOT NULL COMMENT '管理员数据范围：platform/all_tenant/spec_tenant',
  `admin_status` VARCHAR(20) NOT NULL COMMENT 'active/inactive/disabled',
  `last_login_time` DATETIME DEFAULT NULL,
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`admin_id`),
  UNIQUE KEY `uk_admin_account` (`admin_account`),
  KEY `idx_admin_role_id` (`admin_role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='平台管理员表';

-- ###########################################################################
-- 二、租户模块（会计师事务所/IAM用户）
-- ###########################################################################

-- 2.1 租户表（saas_tenant）- 保留核心字段，无外键
CREATE TABLE `saas_tenant` (
  `tenant_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '租户唯一ID（会计师事务所ID）',
  `tenant_name` VARCHAR(100) NOT NULL COMMENT '租户企业名称',
  `business_number` VARCHAR(50) NOT NULL COMMENT '商业登记号（唯一）',
  `ran` VARCHAR(50) NOT NULL COMMENT 'Registered Agent Number（唯一）',
  `onboarding_status` VARCHAR(20) NOT NULL COMMENT 'pending/in_progress/completed',
  `tenant_status` VARCHAR(20) NOT NULL COMMENT 'active/inactive/disabled',
  `contact_email` VARCHAR(100) NOT NULL COMMENT '租户联系邮箱',
  `contact_phone` VARCHAR(20) DEFAULT NULL,
  `tenant_create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `tenant_activate_time` DATETIME DEFAULT NULL,
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`tenant_id`),
  UNIQUE KEY `uk_tenant_business_number` (`business_number`),
  UNIQUE KEY `uk_tenant_ran` (`ran`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户表（会计师事务所）';

-- 2.2 租户IAM用户表（saas_tenant_user）- 保留tenant_id，新增data_owner_id（归属租户ID）
CREATE TABLE `saas_tenant_user` (
  `tenant_user_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '租户员工唯一ID',
  `tenant_id` BIGINT NOT NULL COMMENT '关联saas_tenant.tenant_id（所属租户，核心数据权限字段）',
  `data_owner_id` BIGINT NOT NULL COMMENT '数据归属ID（同tenant_id，数据权限用）',
  `data_owner_type` VARCHAR(20) NOT NULL DEFAULT 'tenant' COMMENT '数据归属类型：tenant',
  `login_email` VARCHAR(100) NOT NULL COMMENT '登录邮箱（唯一）',
  `initial_password` VARCHAR(64) NOT NULL COMMENT '初始化加密密码',
  `password_changed` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '0未修改/1已修改',
  `tenant_user_role` VARCHAR(30) NOT NULL COMMENT '关联saas_role.role_name（租户内角色）',
  `staff_name` VARCHAR(30) DEFAULT NULL,
  `staff_title` VARCHAR(50) DEFAULT NULL,
  `staff_phone` VARCHAR(20) DEFAULT NULL,
  `first_login_status` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '0未完成/1已完成',
  `otp_verified` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '0未验证/1已验证',
  `user_status` VARCHAR(20) NOT NULL COMMENT 'pending/active/disabled',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`tenant_user_id`),
  UNIQUE KEY `uk_tenant_user_email` (`login_email`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_data_owner` (`data_owner_type`,`data_owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户IAM用户表';

-- ###########################################################################
-- 三、用户与客户模块（注册用户/个人客户/企业客户）
-- ###########################################################################

-- 3.1 平台用户表（saas_user）- 新增data_scope（用户数据范围）
CREATE TABLE `saas_user` (
  `user_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户唯一ID',
  `username` VARCHAR(50) NOT NULL COMMENT '登录用户名（唯一）',
  `login_password` VARCHAR(64) NOT NULL COMMENT '加密密码',
  `email` VARCHAR(100) NOT NULL COMMENT '登录邮箱（唯一）',
  `email_verified` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '0未验证/1已验证',
  `user_type` VARCHAR(20) NOT NULL COMMENT 'registered/individual_customer/enterprise_rep',
  `registration_method` VARCHAR(20) NOT NULL COMMENT 'self_register/invited',
  `data_scope` VARCHAR(20) NOT NULL DEFAULT 'self' COMMENT '用户数据范围：self/enterprise',
  `user_status` VARCHAR(20) NOT NULL COMMENT 'inactive/active/disabled',
  `register_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_login_time` DATETIME DEFAULT NULL,
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `uk_user_username` (`username`),
  UNIQUE KEY `uk_user_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='平台用户表';

-- 3.2 租户-客户邀请表（saas_tenant_customer_invite）- 保留tenant_id，新增数据归属字段
CREATE TABLE `saas_tenant_customer_invite` (
  `invite_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '邀请记录唯一ID',
  `tenant_id` BIGINT NOT NULL COMMENT '关联saas_tenant.tenant_id（邀请方租户）',
  `data_owner_id` BIGINT NOT NULL COMMENT '数据归属ID（同tenant_id）',
  `data_owner_type` VARCHAR(20) NOT NULL DEFAULT 'tenant' COMMENT '数据归属类型：tenant',
  `invited_email` VARCHAR(100) NOT NULL COMMENT '被邀请人邮箱',
  `invite_type` VARCHAR(20) NOT NULL COMMENT 'individual/enterprise_rep',
  `invite_status` VARCHAR(20) NOT NULL COMMENT 'pending/accepted/expired/rejected',
  `invite_link` VARCHAR(255) NOT NULL COMMENT '邀请链接（唯一）',
  `link_expire_time` DATETIME NOT NULL,
  `invite_send_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `accept_time` DATETIME DEFAULT NULL,
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`invite_id`),
  UNIQUE KEY `uk_invite_link` (`invite_link`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_invited_email_status` (`invited_email`,`invite_status`),
  KEY `idx_data_owner` (`data_owner_type`,`data_owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户-客户邀请表';

-- 3.3 个人客户KYC表（saas_individual_kyc）- 保留user_id，新增数据归属字段
CREATE TABLE `saas_individual_kyc` (
  `kyc_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'KYC记录唯一ID',
  `user_id` BIGINT NOT NULL COMMENT '关联saas_user.user_id（所属个人客户）',
  `data_owner_id` BIGINT NOT NULL COMMENT '数据归属ID（同user_id）',
  `data_owner_type` VARCHAR(20) NOT NULL DEFAULT 'user' COMMENT '数据归属类型：user',
  `my_id` VARCHAR(50) NOT NULL COMMENT '澳大利亚MyID（唯一）',
  `kyc_data` JSON NOT NULL COMMENT 'KYC详细数据（JSON）',
  `kyc_status` VARCHAR(20) NOT NULL COMMENT 'pending_review/approved/rejected/expired',
  `kyc_submit_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `kyc_expire_time` DATETIME NOT NULL,
  `kyc_approved_time` DATETIME DEFAULT NULL,
  `reviewer_id` BIGINT DEFAULT NULL COMMENT '关联saas_platform_admin.admin_id（审核人）',
  `review_notes` VARCHAR(500) DEFAULT NULL,
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`kyc_id`),
  UNIQUE KEY `uk_individual_myid` (`my_id`),
  UNIQUE KEY `uk_individual_user_kyc` (`user_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_kyc_status_expire` (`kyc_status`,`kyc_expire_time`),
  KEY `idx_data_owner` (`data_owner_type`,`data_owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='个人客户KYC表';

-- 3.4 企业客户表（saas_enterprise_customer）- 保留authorized_user_id，新增数据归属字段
CREATE TABLE `saas_enterprise_customer` (
  `enterprise_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '企业客户唯一ID',
  `enterprise_name` VARCHAR(100) NOT NULL COMMENT '企业名称',
  `business_number` VARCHAR(50) NOT NULL COMMENT '商业登记号（唯一）',
  `ran` VARCHAR(50) DEFAULT NULL,
  `enterprise_status` VARCHAR(20) NOT NULL COMMENT 'active/inactive/cancelled',
  `kyb_status` VARCHAR(20) NOT NULL COMMENT 'pending_review/approved/rejected/expired',
  `authorized_user_id` BIGINT NOT NULL COMMENT '关联saas_user.user_id（授权代表）',
  `data_owner_id` BIGINT NOT NULL COMMENT '数据归属ID（同enterprise_id）',
  `data_owner_type` VARCHAR(20) NOT NULL DEFAULT 'enterprise' COMMENT '数据归属类型：enterprise',
  `kyb_submit_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `kyb_expire_time` DATETIME NOT NULL,
  `enterprise_address` VARCHAR(255) DEFAULT NULL,
  `contact_phone` VARCHAR(20) DEFAULT NULL,
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`enterprise_id`),
  UNIQUE KEY `uk_enterprise_business_number` (`business_number`),
  KEY `idx_authorized_user_id` (`authorized_user_id`),
  KEY `idx_kyb_status_expire` (`kyb_status`,`kyb_expire_time`),
  KEY `idx_data_owner` (`data_owner_type`,`data_owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业客户表';

-- 3.5 企业客户KYB表（saas_enterprise_kyb）- 保留enterprise_id，新增数据归属字段
CREATE TABLE `saas_enterprise_kyb` (
  `kyb_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'KYB记录唯一ID',
  `enterprise_id` BIGINT NOT NULL COMMENT '关联saas_enterprise_customer.enterprise_id',
  `data_owner_id` BIGINT NOT NULL COMMENT '数据归属ID（同enterprise_id）',
  `data_owner_type` VARCHAR(20) NOT NULL DEFAULT 'enterprise' COMMENT '数据归属类型：enterprise',
  `kyb_data` JSON NOT NULL COMMENT 'KYB详细数据（JSON）',
  `kyb_approved_time` DATETIME DEFAULT NULL,
  `reviewer_id` BIGINT DEFAULT NULL COMMENT '关联saas_platform_admin.admin_id（审核人）',
  `review_notes` VARCHAR(500) DEFAULT NULL,
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`kyb_id`),
  UNIQUE KEY `uk_enterprise_kyb` (`enterprise_id`),
  KEY `idx_enterprise_id` (`enterprise_id`),
  KEY `idx_data_owner` (`data_owner_type`,`data_owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业客户KYB表';

-- ###########################################################################
-- 四、客户授权模块（个人/企业授权）
-- ###########################################################################

-- 4.1 个人客户授权表（saas_individual_auth）- 保留user_id/tenant_id，新增数据归属
CREATE TABLE `saas_individual_auth` (
  `auth_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '授权记录唯一ID',
  `user_id` BIGINT NOT NULL COMMENT '关联saas_user.user_id（授权方）',
  `tenant_id` BIGINT NOT NULL COMMENT '关联saas_tenant.tenant_id（被授权方）',
  `data_owner_id` BIGINT NOT NULL COMMENT '数据归属ID（同tenant_id，租户数据权限用）',
  `data_owner_type` VARCHAR(20) NOT NULL DEFAULT 'tenant' COMMENT '数据归属类型：tenant',
  `auth_business_type` VARCHAR(50) NOT NULL COMMENT '授权业务类型（自包含）',
  `auth_status` VARCHAR(20) NOT NULL COMMENT 'pending_signature/signed/authorized/expired/cancelled',
  `docusign_envelope_id` VARCHAR(100) DEFAULT NULL,
  `auth_effective_time` DATETIME NOT NULL,
  `auth_expire_time` DATETIME NOT NULL,
  `ato_agent_add_status` VARCHAR(20) NOT NULL COMMENT 'pending/completed/failed',
  `reminder_days` INT NOT NULL DEFAULT 30,
  `reminder_sent` TINYINT(1) NOT NULL DEFAULT 0,
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`auth_id`),
  UNIQUE KEY `uk_individual_auth_unique` (`user_id`,`tenant_id`,`auth_business_type`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_auth_status_expire` (`auth_status`,`auth_expire_time`),
  KEY `idx_data_owner` (`data_owner_type`,`data_owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='个人客户授权表';

-- 4.2 企业客户授权表（saas_enterprise_auth）- 保留enterprise_id/tenant_id，新增数据归属
CREATE TABLE `saas_enterprise_auth` (
  `auth_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '授权记录唯一ID',
  `enterprise_id` BIGINT NOT NULL COMMENT '关联saas_enterprise_customer.enterprise_id（授权方）',
  `tenant_id` BIGINT NOT NULL COMMENT '关联saas_tenant.tenant_id（被授权方）',
  `data_owner_id` BIGINT NOT NULL COMMENT '数据归属ID（同tenant_id）',
  `data_owner_type` VARCHAR(20) NOT NULL DEFAULT 'tenant' COMMENT '数据归属类型：tenant',
  `auth_business_type` VARCHAR(50) NOT NULL COMMENT '授权业务类型（自包含）',
  `auth_status` VARCHAR(20) NOT NULL COMMENT 'pending_signature/signed/authorized/expired/cancelled',
  `docusign_envelope_id` VARCHAR(100) DEFAULT NULL,
  `auth_effective_time` DATETIME NOT NULL,
  `auth_expire_time` DATETIME NOT NULL,
  `ato_nomination_status` VARCHAR(20) NOT NULL COMMENT 'pending/completed/failed',
  `reminder_days` INT NOT NULL DEFAULT 30,
  `reminder_sent` TINYINT(1) NOT NULL DEFAULT 0,
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`auth_id`),
  UNIQUE KEY `uk_enterprise_auth_unique` (`enterprise_id`,`tenant_id`,`auth_business_type`),
  KEY `idx_enterprise_id` (`enterprise_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_auth_status_expire` (`auth_status`,`auth_expire_time`),
  KEY `idx_data_owner` (`data_owner_type`,`data_owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业客户授权表';

-- ###########################################################################
-- 五、安全验证模块（OTP）
-- ###########################################################################

-- 5.1 OTP记录表（saas_otp_record）- 保留target_id/target_type，新增数据归属
CREATE TABLE `saas_otp_record` (
  `otp_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'OTP记录唯一ID',
  `target_id` BIGINT NOT NULL COMMENT '关联user_id/tenant_user_id',
  `target_type` VARCHAR(20) NOT NULL COMMENT 'user/tenant_user',
  `data_owner_id` BIGINT NOT NULL COMMENT '数据归属ID（同target_id）',
  `data_owner_type` VARCHAR(20) NOT NULL COMMENT '数据归属类型（同target_type）',
  `otp_type` VARCHAR(20) NOT NULL COMMENT 'first_login/password_reset',
  `otp_code` VARCHAR(64) NOT NULL COMMENT '加密OTP码',
  `otp_expire_time` DATETIME NOT NULL,
  `verify_status` VARCHAR(20) NOT NULL COMMENT 'unverified/verified/expired',
  `send_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `verify_time` DATETIME DEFAULT NULL,
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`otp_id`),
  KEY `idx_target_otp` (`target_id`,`target_type`,`otp_type`,`verify_status`),
  KEY `idx_otp_expire` (`otp_expire_time`,`verify_status`),
  KEY `idx_data_owner` (`data_owner_type`,`data_owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='OTP记录表';

-- ###########################################################################
-- 六、数据权限模块（强化数据权限字段）
-- ###########################################################################

-- 6.1 数据权限类型表（saas_data_permission）- 新增默认数据范围
CREATE TABLE `saas_data_permission` (
  `data_perm_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '数据权限唯一ID',
  `data_perm_name` VARCHAR(50) NOT NULL COMMENT '数据权限名称（唯一）',
  `data_perm_type` VARCHAR(20) NOT NULL COMMENT 'tenant_scope/customer_scope/platform_scope',
  `default_data_scope` VARCHAR(50) NOT NULL COMMENT '默认数据范围：如“tenant_id = #{currentTenantId}”',
  `data_perm_desc` VARCHAR(200) NOT NULL COMMENT '权限描述',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`data_perm_id`),
  UNIQUE KEY `uk_data_perm_name` (`data_perm_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据权限类型表';

-- 6.2 角色-数据权限关联表（saas_role_data_perm_rel）- 强化tenant_id非空（租户级必传）
CREATE TABLE `saas_role_data_perm_rel` (
  `rel_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '关联记录唯一ID',
  `role_id` INT NOT NULL COMMENT '关联saas_role.role_id',
  `data_perm_id` BIGINT NOT NULL COMMENT '关联saas_data_permission.data_perm_id',
  `tenant_id` BIGINT NOT NULL COMMENT '租户ID：平台级权限填0，租户级填实际tenant_id',
  `data_owner_id` BIGINT NOT NULL COMMENT '数据归属ID（同tenant_id）',
  `data_owner_type` VARCHAR(20) NOT NULL DEFAULT 'tenant' COMMENT '数据归属类型：tenant',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`rel_id`),
  UNIQUE KEY `uk_role_data_perm_tenant` (`role_id`,`data_perm_id`,`tenant_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_data_perm_id` (`data_perm_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_data_owner` (`data_owner_type`,`data_owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色-数据权限关联表';

-- 6.3 角色-指定客户数据范围表（saas_role_customer_scope）- 强化租户+客户关联
CREATE TABLE `saas_role_customer_scope` (
  `scope_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '范围记录唯一ID',
  `role_id` INT NOT NULL COMMENT '关联saas_role.role_id',
  `tenant_id` BIGINT NOT NULL COMMENT '关联saas_tenant.tenant_id（所属租户）',
  `user_id` BIGINT DEFAULT NULL COMMENT '关联saas_user.user_id（个人客户，二选一）',
  `enterprise_id` BIGINT DEFAULT NULL COMMENT '关联saas_enterprise_customer.enterprise_id（企业客户，二选一）',
  `data_owner_id` BIGINT NOT NULL COMMENT '数据归属ID（tenant_id，租户级数据权限）',
  `data_owner_type` VARCHAR(20) NOT NULL DEFAULT 'tenant' COMMENT '数据归属类型：tenant',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expire_time` DATETIME DEFAULT NULL,
  PRIMARY KEY (`scope_id`),
  UNIQUE KEY `uk_role_tenant_customer` (`role_id`,`tenant_id`,`user_id`,`enterprise_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_enterprise_id` (`enterprise_id`),
  KEY `idx_data_owner` (`data_owner_type`,`data_owner_id`),
  CONSTRAINT `ck_customer_scope` CHECK ((`user_id` IS NOT NULL) OR (`enterprise_id` IS NOT NULL))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色-指定客户数据范围表';

-- ###########################################################################
-- 七、初始化基础数据（含数据权限配置）
-- ###########################################################################

-- 7.1 初始化基础角色（含data_scope）
INSERT INTO `saas_role` (`role_name`, `role_type`, `role_description`, `data_scope`) VALUES
('super_admin', 'platform_level', '超级管理员，拥有平台全量权限', 'platform'),
('platform_admin', 'platform_level', '平台管理员，负责租户/KYC审核', 'all_tenant'),
('tenant_admin', 'tenant_level', '租户管理员，管理本所全量数据', 'tenant'),
('tenant_staff', 'tenant_level', '租户员工，处理本所客户业务', 'tenant'),
('tax_agent', 'tenant_level', '税务代理，仅访问指定客户数据', 'customer');

-- 7.2 初始化基础数据权限（含default_data_scope）
INSERT INTO `saas_data_permission` (`data_perm_name`, `data_perm_type`, `default_data_scope`, `data_perm_desc`) VALUES
('platform_all_data', 'platform_scope', '1=1', '平台全量数据：无数据过滤'),
('tenant_all_data', 'tenant_scope', 'tenant_id = #{currentTenantId}', '租户内全量数据：过滤当前租户ID'),
('tenant_customer_individual', 'customer_scope', 'user_id IN (#{customerIds})', '租户指定个人客户数据：过滤指定客户ID'),
('tenant_customer_enterprise', 'customer_scope', 'enterprise_id IN (#{customerIds})', '租户指定企业客户数据：过滤指定客户ID');

-- 7.3 初始化角色-数据权限关联（含tenant_id）
INSERT INTO `saas_role_data_perm_rel` (`role_id`, `data_perm_id`, `tenant_id`, `data_owner_id`, `data_owner_type`)
VALUES
-- 超级管理员：平台全量数据权限（tenant_id=0标识平台级）
((SELECT `role_id` FROM `saas_role` WHERE `role_name`='super_admin'),
 (SELECT `data_perm_id` FROM `saas_data_permission` WHERE `data_perm_name`='platform_all_data'),
 0, 0, 'tenant'),
-- 租户管理员：租户内全量数据权限（tenant_id=0占位，实际租户创建时动态替换）
((SELECT `role_id` FROM `saas_role` WHERE `role_name`='tenant_admin'),
 (SELECT `data_perm_id` FROM `saas_data_permission` WHERE `data_perm_name`='tenant_all_data'),
 0, 0, 'tenant');

-- ###########################################################################
-- 会计师事务所SaaS系统 - 含前端菜单+控件权限（无外键+数据权限版）
-- 新增模块：前端菜单管理、页面控件管理、角色-菜单/控件关联
-- ###########################################################################

-- 1. 基础配置（同前，略）
CREATE DATABASE IF NOT EXISTS accounting_saas 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_unicode_ci;

USE accounting_saas;
SET default_storage_engine = INNODB;

-- ###########################################################################
-- （原有表结构：角色/权限/租户/用户等，同前，此处省略，直接补充新增前端权限表）
-- ###########################################################################

-- ###########################################################################
-- 七、前端菜单权限模块（新增）
-- ###########################################################################

-- 7.1 前端菜单表（saas_frontend_menu）- 控制菜单可见性
CREATE TABLE `saas_frontend_menu` (
  `menu_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '菜单唯一ID',
  `menu_name` VARCHAR(50) NOT NULL COMMENT '菜单名称（如“租户管理”“客户KYC”）',
  `parent_menu_id` BIGINT NOT NULL DEFAULT 0 COMMENT '父菜单ID：0=一级菜单，>0=子菜单',
  `menu_url` VARCHAR(255) NOT NULL COMMENT '前端路由地址（如“/tenant/list”）',
  `menu_icon` VARCHAR(50) DEFAULT NULL COMMENT '菜单图标（如“el-icon-s-tools”）',
  `menu_sort` INT NOT NULL DEFAULT 0 COMMENT '菜单排序号（数字越小越靠前）',
  `menu_type` VARCHAR(20) NOT NULL COMMENT '菜单类型：directory（目录）/menu（菜单）/button（按钮，特殊菜单）',
  `visible_status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否默认显示：1=显示，0=隐藏（需权限控制）',
  `data_owner_type` VARCHAR(20) NOT NULL DEFAULT 'platform' COMMENT '数据归属类型：platform/tenant',
  `data_owner_id` BIGINT NOT NULL DEFAULT 0 COMMENT '数据归属ID：平台级=0，租户级=tenant_id',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`menu_id`),
  UNIQUE KEY `uk_menu_url` (`menu_url`) COMMENT '前端路由唯一，避免重复菜单',
  KEY `idx_parent_menu_id` (`parent_menu_id`),
  KEY `idx_data_owner` (`data_owner_type`,`data_owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='前端菜单表（控制菜单可见性）';

-- 7.2 角色-前端菜单关联表（saas_role_frontend_menu_rel）- 角色绑定菜单权限
CREATE TABLE `saas_role_frontend_menu_rel` (
  `rel_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '关联记录唯一ID',
  `role_id` INT NOT NULL COMMENT '关联saas_role.role_id（角色ID）',
  `menu_id` BIGINT NOT NULL COMMENT '关联saas_frontend_menu.menu_id（菜单ID）',
  `permission_id` INT DEFAULT NULL COMMENT '关联saas_permission.permission_id（后端功能权限ID，确保前后端权限一致）',
  `tenant_id` BIGINT NOT NULL DEFAULT 0 COMMENT '租户ID：平台级角色=0，租户级角色=实际tenant_id',
  `data_owner_type` VARCHAR(20) NOT NULL DEFAULT 'tenant' COMMENT '数据归属类型：tenant',
  `data_owner_id` BIGINT NOT NULL DEFAULT 0 COMMENT '数据归属ID（同tenant_id）',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`rel_id`),
  UNIQUE KEY `uk_role_menu` (`role_id`,`menu_id`) COMMENT '同一角色不重复绑定同一菜单',
  KEY `idx_role_id` (`role_id`),
  KEY `idx_menu_id` (`menu_id`),
  KEY `idx_data_owner` (`data_owner_type`,`data_owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色-前端菜单关联表（控制角色可见菜单）';

-- ###########################################################################
-- 八、前端控件权限模块（新增）
-- ###########################################################################

-- 8.1 前端页面控件表（saas_frontend_control）- 控制按钮/开关等控件可用性
CREATE TABLE `saas_frontend_control` (
  `control_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '控件唯一ID',
  `control_name` VARCHAR(50) NOT NULL COMMENT '控件名称（如“新增租户按钮”“审核KYC开关”）',
  `menu_id` BIGINT NOT NULL COMMENT '关联saas_frontend_menu.menu_id（所属菜单，必选）',
  `control_selector` VARCHAR(100) NOT NULL COMMENT '前端控件唯一标识（如“tenant-add-btn”“kyc-review-switch”）',
  `control_type` VARCHAR(20) NOT NULL COMMENT '控件类型：button（按钮）/switch（开关）/input（输入框）/select（下拉框）',
  `control_action` VARCHAR(50) DEFAULT NULL COMMENT '控件触发动作（如“add”“delete”“review”）',
  `default_status` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '默认状态：0=禁用/隐藏，1=启用/显示（需权限控制）',
  `data_owner_type` VARCHAR(20) NOT NULL DEFAULT 'platform' COMMENT '数据归属类型：platform/tenant',
  `data_owner_id` BIGINT NOT NULL DEFAULT 0 COMMENT '数据归属ID：平台级=0，租户级=tenant_id',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`control_id`),
  UNIQUE KEY `uk_menu_control_selector` (`menu_id`,`control_selector`) COMMENT '同一菜单下控件标识唯一',
  KEY `idx_menu_id` (`menu_id`),
  KEY `idx_data_owner` (`data_owner_type`,`data_owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='前端页面控件表（控制按钮/开关可用性）';

-- 8.2 角色-前端控件关联表（saas_role_frontend_control_rel）- 角色绑定控件权限
CREATE TABLE `saas_role_frontend_control_rel` (
  `rel_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '关联记录唯一ID',
  `role_id` INT NOT NULL COMMENT '关联saas_role.role_id（角色ID）',
  `control_id` BIGINT NOT NULL COMMENT '关联saas_frontend_control.control_id（控件ID）',
  `permission_id` INT NOT NULL COMMENT '关联saas_permission.permission_id（后端功能权限ID，必关联，确保前后端一致）',
  `tenant_id` BIGINT NOT NULL DEFAULT 0 COMMENT '租户ID：平台级角色=0，租户级角色=实际tenant_id',
  `control_status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '控件权限状态：1=启用/显示，0=禁用/隐藏',
  `data_owner_type` VARCHAR(20) NOT NULL DEFAULT 'tenant' COMMENT '数据归属类型：tenant',
  `data_owner_id` BIGINT NOT NULL DEFAULT 0 COMMENT '数据归属ID（同tenant_id）',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`rel_id`),
  UNIQUE KEY `uk_role_control` (`role_id`,`control_id`) COMMENT '同一角色不重复绑定同一控件',
  KEY `idx_role_id` (`role_id`),
  KEY `idx_control_id` (`control_id`),
  KEY `idx_permission_id` (`permission_id`),
  KEY `idx_data_owner` (`data_owner_type`,`data_owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色-前端控件关联表（控制角色可用控件）';

-- ###########################################################################
-- 九、初始化前端权限基础数据（新增）
-- ###########################################################################

-- 9.1 初始化一级菜单（平台级+租户级）
INSERT INTO `saas_frontend_menu` (`menu_name`, `parent_menu_id`, `menu_url`, `menu_icon`, `menu_sort`, `menu_type`, `visible_status`, `data_owner_type`, `data_owner_id`)
VALUES
-- 平台级菜单（数据归属ID=0）
('系统管理', 0, '/system/manage', 'el-icon-s-tools', 1, 'directory', 1, 'platform', 0),
('租户管理', 1, '/tenant/list', 'el-icon-s-company', 1, 'menu', 1, 'platform', 0),
('平台用户管理', 1, '/platform/user/list', 'el-icon-user', 2, 'menu', 1, 'platform', 0),
-- 租户级菜单（数据归属ID=0，租户创建时动态继承）
('客户管理', 0, '/customer/manage', 'el-icon-user-solid', 2, 'directory', 1, 'tenant', 0),
('个人客户KYC', 4, '/customer/individual/kyc', 'el-icon-id-card', 1, 'menu', 1, 'tenant', 0),
('企业客户KYB', 4, '/customer/enterprise/kyb', 'el-icon-building', 2, 'menu', 1, 'tenant', 0),
('业务授权管理', 0, '/auth/manage', 'el-icon-key', 3, 'directory', 1, 'tenant', 0),
('个人授权列表', 7, '/auth/individual/list', 'el-icon-user-check', 1, 'menu', 1, 'tenant', 0),
('企业授权列表', 7, '/auth/enterprise/list', 'el-icon-building-check', 2, 'menu', 1, 'tenant', 0);

-- 9.2 初始化菜单下的控件（如“新增租户”按钮、“审核KYC”开关）
INSERT INTO `saas_frontend_control` (`control_name`, `menu_id`, `control_selector`, `control_type`, `control_action`, `default_status`, `data_owner_type`, `data_owner_id`)
VALUES
-- 租户管理菜单控件（menu_id=2，对应“租户管理”菜单）
('新增租户按钮', 2, 'tenant-add-btn', 'button', 'add', 0, 'platform', 0),
('编辑租户按钮', 2, 'tenant-edit-btn', 'button', 'edit', 0, 'platform', 0),
('删除租户按钮', 2, 'tenant-delete-btn', 'button', 'delete', 0, 'platform', 0),
-- 个人客户KYC菜单控件（menu_id=5，对应“个人客户KYC”菜单）
('审核KYC开关', 5, 'kyc-review-switch', 'switch', 'review', 0, 'tenant', 0),
('驳回KYC按钮', 5, 'kyc-reject-btn', 'button', 'reject', 0, 'tenant', 0),
('查看KYC详情按钮', 5, 'kyc-view-btn', 'button', 'view', 1, 'tenant', 0); -- 默认显示查看按钮

-- 9.3 初始化角色-菜单关联（超级管理员可见所有菜单）
INSERT INTO `saas_role_frontend_menu_rel` (`role_id`, `menu_id`, `permission_id`, `tenant_id`, `data_owner_type`, `data_owner_id`)
SELECT
  (SELECT `role_id` FROM `saas_role` WHERE `role_name`='super_admin'), -- 超级管理员角色
  `menu_id`, -- 所有菜单
  NULL, -- 平台级菜单暂不关联后端权限
  0, -- 平台级角色tenant_id=0
  'tenant',
  0
FROM `saas_frontend_menu`;

-- 9.4 初始化角色-控件关联（超级管理员拥有所有控件权限，关联后端功能权限）
INSERT INTO `saas_role_frontend_control_rel` (`role_id`, `control_id`, `permission_id`, `tenant_id`, `control_status`, `data_owner_type`, `data_owner_id`)
VALUES
-- 超级管理员：新增租户控件（关联“租户创建”后端权限）
((SELECT `role_id` FROM `saas_role` WHERE `role_name`='super_admin'),
 (SELECT `control_id` FROM `saas_frontend_control` WHERE `control_selector`='tenant-add-btn'),
 (SELECT `permission_id` FROM `saas_permission` WHERE `permission_name`='tenant_create'), -- 后端功能权限
 0, 1, 'tenant', 0),
-- 超级管理员：审核KYC控件（关联“KYC审核”后端权限）
((SELECT `role_id` FROM `saas_role` WHERE `role_name`='super_admin'),
 (SELECT `control_id` FROM `saas_frontend_control` WHERE `control_selector`='kyc-review-switch'),
 (SELECT `permission_id` FROM `saas_permission` WHERE `permission_name`='customer_kyc_review'), -- 后端功能权限
 0, 1, 'tenant', 0);
