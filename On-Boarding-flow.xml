<mxfile>
  <diagram id="123456" name="会计师事务所SaaS平台Onboard全流程">
    <mxGraphModel>
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        <!-- 1. 平台初始化节点 -->
        <mxCell id="2" value="<b>平台启动</b><BR>超级管理员/平台管理员登录" style="shape=ellipse;fillColor=#f0f8ff;strokeColor=#4169e1;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="100" y="50" width="120" height="60" as="geometry"/>
        </mxCell>
        <!-- 2. 租户Onboard分支 -->
        <mxCell id="3" value="<b>租户Onboard触发</b><BR>会计师事务所发起Onboard" style="shape=ellipse;fillColor=#f0fff0;strokeColor=#32cd32;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="100" y="200" width="140" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="4" value="租户填写核心信息：<BR>企业名称/Business Number/RAN" style="shape=rectangle;fillColor=#fff8dc;strokeColor=#d2b48c;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="100" y="300" width="160" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="5" value="添加租户员工邮箱，指定角色：<BR>管理员/员工/Tax Agent" style="shape=rectangle;fillColor=#fff8dc;strokeColor=#d2b48c;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="100" y="420" width="160" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="6" value="生成初始化密码，发送登录邮件" style="shape=rectangle;fillColor=#fff8dc;strokeColor=#d2b48c;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="100" y="540" width="160" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="7" value="员工首次登录：<BR>1. 补充基本信息<BR>2. 邮箱OTP验证<BR>3. 强制修改密码" style="shape=rectangle;fillColor=#fff8dc;strokeColor=#d2b48c;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="100" y="640" width="160" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="8" value="<b>租户Onboard完成</b>" style="shape=ellipse;fillColor=#f0fff0;strokeColor=#32cd32;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="100" y="760" width="120" height="60" as="geometry"/>
        </mxCell>
        <!-- 3. 用户Onboard分支（自主注册+受邀注册） -->
        <mxCell id="9" value="<b>用户Onboard触发</b><BR>自主注册 / 受邀注册" style="shape=ellipse;fillColor=#fff0f5;strokeColor=#db7093;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="400" y="200" width="140" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="10" value="分支1：自主注册<BR>填写：用户名/密码/邮箱（OTP验证）" style="shape=rectangle;fillColor=#fff8dc;strokeColor=#d2b48c;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="300" y="300" width="140" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="11" value="分支2：受邀注册（会计师事务所邀请）" style="shape=rectangle;fillColor=#fff8dc;strokeColor=#d2b48c;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="500" y="300" width="160" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="12" value="检查邮箱是否已注册：<BR>是→发送绑定通知<BR>否→创建账号+初始密码" style="shape=rectangle;fillColor=#fff8dc;strokeColor=#d2b48c;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="500" y="400" width="180" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="13" value="<b>用户Onboard完成</b><BR>（可后续KYC升级为客户）" style="shape=ellipse;fillColor=#fff0f5;strokeColor=#db7093;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="400" y="520" width="160" height="60" as="geometry"/>
        </mxCell>
        <!-- 4. 客户Onboard分支（个人+企业） -->
        <mxCell id="14" value="<b>客户Onboard触发</b><BR>个人主动发起 / 受租户邀请" style="shape=ellipse;fillColor=#f0f8ff;strokeColor=#4169e1;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="700" y="200" width="160" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="15" value="检查是否已Onboard：<BR>是→跳过KYC<BR>否→完成KYC（含MyID）" style="shape=rectangle;fillColor=#fff8dc;strokeColor=#d2b48c;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="700" y="300" width="160" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="16" value="生成个人客户ID，绑定用户登录" style="shape=rectangle;fillColor=#fff8dc;strokeColor=#d2b48c;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="700" y="420" width="160" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="17" value="企业客户Onboard：<BR>需授权个人客户发起→检查企业信息+授权关系→生成企业客户ID" style="shape=rectangle;fillColor=#fff8dc;strokeColor=#d2b48c;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="700" y="520" width="180" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="18" value="业务授权：<BR>DocuSign签署→绑定客户ID与租户→ATO Agent操作" style="shape=rectangle;fillColor=#fff8dc;strokeColor=#d2b48c;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="700" y="640" width="160" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="19" value="<b>客户Onboard+授权完成</b><BR>（到期前邮件提醒更新）" style="shape=ellipse;fillColor=#f0f8ff;strokeColor=#4169e1;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="700" y="760" width="180" height="60" as="geometry"/>
        </mxCell>
        <!-- 连接线 -->
        <mxCell id="20" value="" style="shape=connector;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="160" y="110" as="source"/>
            <mxPoint x="160" y="200" as="target"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="21" value="" style="shape=connector;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="170" y="260" as="source"/>
            <mxPoint x="170" y="300" as="target"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="22" value="" style="shape=connector;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="170" y="380" as="source"/>
            <mxPoint x="170" y="420" as="target"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="23" value="" style="shape=connector;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="170" y="500" as="source"/>
            <mxPoint x="170" y="540" as="target"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="24" value="" style="shape=connector;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="170" y="600" as="source"/>
            <mxPoint x="170" y="640" as="target"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="25" value="" style="shape=connector;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="170" y="720" as="source"/>
            <mxPoint x="170" y="760" as="target"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="26" value="" style="shape=connector;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="160" y="110" as="source"/>
            <mxPoint x="470" y="200" as="target"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="27" value="" style="shape=connector;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="470" y="260" as="source"/>
            <mxPoint x="370" y="300" as="target"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="28" value="" style="shape=connector;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="470" y="260" as="source"/>
            <mxPoint x="580" y="300" as="target"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="29" value="" style="shape=connector;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="580" y="360" as="source"/>
            <mxPoint x="580" y="400" as="target"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="30" value="" style="shape=connector;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="370" y="380" as="source"/>
            <mxPoint x="480" y="520" as="target"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="31" value="" style="shape=connector;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="580" y="480" as="source"/>
            <mxPoint x="480" y="520" as="target"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="32" value="" style="shape=connector;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="160" y="110" as="source"/>
            <mxPoint x="780" y="200" as="target"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="33" value="" style="shape=connector;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="780" y="260" as="source"/>
            <mxPoint x="780" y="300" as="target"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="34" value="" style="shape=connector;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="780" y="380" as="source"/>
            <mxPoint x="780" y="420" as="target"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="35" value="" style="shape=connector;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="780" y="480" as="source"/>
            <mxPoint x="780" y="520" as="target"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="36" value="" style="shape=connector;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="780" y="600" as="source"/>
            <mxPoint x="780" y="640" as="target"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="37" value="" style="shape=connector;strokeColor=#666666;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="780" y="720" as="source"/>
            <mxPoint x="780" y="760" as="target"/>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>