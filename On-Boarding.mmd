erDiagram
    %% ===================== 1. 平台基础模块（RBAC核心） =====================
    saas_role {
        role_id INT PK "角色唯一ID"
        role_name VARCHAR(30) UK "角色名称（唯一）：super_admin、tenant_admin"
        role_type VARCHAR(20) NN "角色层级：platform_level/tenant_level"
        role_description VARCHAR(200) "" "角色描述"
        data_scope VARCHAR(20) NN "角色默认数据范围：platform/tenant/customer"
        create_time DATETIME NN "创建时间"
        update_time DATETIME NN "更新时间"
    }

    saas_permission {
        permission_id INT PK "权限唯一ID"
        permission_name VARCHAR(50) UK "权限名称（唯一）"
        permission_desc VARCHAR(200) "" "权限描述"
        permission_type VARCHAR(20) NN "system/business"
        data_perm_flag TINYINT(1) NN "是否需数据权限：0否1是"
        create_time DATETIME NN "创建时间"
        update_time DATETIME NN "更新时间"
    }

    saas_role_permission {
        id BIGINT PK "关联记录唯一ID"
        role_id INT FK "关联saas_role.role_id"
        permission_id INT FK "关联saas_permission.permission_id"
        create_time DATETIME NN "创建时间"
    }

    saas_platform_admin {
        admin_id BIGINT PK "管理员唯一ID"
        admin_account VARCHAR(50) UK "登录账号（唯一）"
        login_password VARCHAR(64) NN "加密密码（SHA256+盐值）"
        admin_name VARCHAR(30) NN "管理员姓名"
        admin_role_id INT FK "关联saas_role.role_id（平台级角色）"
        data_scope VARCHAR(20) NN "管理员数据范围：platform/all_tenant/spec_tenant"
        admin_status VARCHAR(20) NN "active/inactive/disabled"
        last_login_time DATETIME "" "最后登录时间"
        create_time DATETIME NN "创建时间"
        update_time DATETIME NN "更新时间"
    }

    %% ===================== 2. 租户模块（会计师事务所） =====================
    saas_tenant {
        tenant_id BIGINT PK "租户唯一ID（会计师事务所ID）"
        tenant_name VARCHAR(100) NN "租户企业名称"
        business_number VARCHAR(50) UK "商业登记号（唯一）"
        ran VARCHAR(50) UK "Registered Agent Number（唯一）"
        onboarding_status VARCHAR(20) NN "pending/in_progress/completed"
        tenant_status VARCHAR(20) NN "active/inactive/disabled"
        contact_email VARCHAR(100) NN "租户联系邮箱"
        contact_phone VARCHAR(20) "" "联系电话"
        tenant_create_time DATETIME NN "租户创建时间"
        tenant_activate_time DATETIME "" "租户激活时间"
        update_time DATETIME NN "更新时间"
    }

    saas_tenant_user {
        tenant_user_id BIGINT PK "租户员工唯一ID"
        tenant_id BIGINT FK "关联saas_tenant.tenant_id（所属租户，核心数据权限字段）"
        data_owner_id BIGINT NN "数据归属ID（同tenant_id，数据权限用）"
        data_owner_type VARCHAR(20) NN "数据归属类型：tenant"
        login_email VARCHAR(100) UK "登录邮箱（唯一）"
        initial_password VARCHAR(64) NN "初始化加密密码"
        password_changed TINYINT(1) NN "0未修改/1已修改"
        tenant_user_role VARCHAR(30) FK "关联saas_role.role_name（租户内角色）"
        staff_name VARCHAR(30) "" "员工姓名"
        staff_title VARCHAR(50) "" "员工职位"
        staff_phone VARCHAR(20) "" "员工电话"
        first_login_status TINYINT(1) NN "0未完成/1已完成"
        otp_verified TINYINT(1) NN "0未验证/1已验证"
        user_status VARCHAR(20) NN "pending/active/disabled"
        create_time DATETIME NN "创建时间"
        update_time DATETIME NN "更新时间"
    }

    %% ===================== 3. 用户与客户模块 =====================
    saas_user {
        user_id BIGINT PK "用户唯一ID"
        username VARCHAR(50) UK "登录用户名（唯一）"
        login_password VARCHAR(64) NN "加密密码"
        email VARCHAR(100) UK "登录邮箱（唯一）"
        email_verified TINYINT(1) NN "0未验证/1已验证"
        user_type VARCHAR(20) NN "registered/individual_customer/enterprise_rep"
        registration_method VARCHAR(20) NN "self_register/invited"
        data_scope VARCHAR(20) NN "用户数据范围：self/enterprise"
        user_status VARCHAR(20) NN "inactive/active/disabled"
        register_time DATETIME NN "注册时间"
        last_login_time DATETIME "" "最后登录时间"
        create_time DATETIME NN "创建时间"
        update_time DATETIME NN "更新时间"
    }

    saas_tenant_customer_invite {
        invite_id BIGINT PK "邀请记录唯一ID"
        tenant_id BIGINT FK "关联saas_tenant.tenant_id（邀请方租户）"
        data_owner_id BIGINT NN "数据归属ID（同tenant_id）"
        data_owner_type VARCHAR(20) NN "数据归属类型：tenant"
        invited_email VARCHAR(100) NN "被邀请人邮箱"
        invite_type VARCHAR(20) NN "individual/enterprise_rep"
        invite_status VARCHAR(20) NN "pending/accepted/expired/rejected"
        invite_link VARCHAR(255) UK "邀请链接（唯一）"
        link_expire_time DATETIME NN "链接过期时间"
        invite_send_time DATETIME NN "邀请发送时间"
        accept_time DATETIME "" "接受时间"
        create_time DATETIME NN "创建时间"
        update_time DATETIME NN "更新时间"
    }

    saas_individual_kyc {
        kyc_id BIGINT PK "KYC记录唯一ID"
        user_id BIGINT FK "关联saas_user.user_id（所属个人客户）"
        data_owner_id BIGINT NN "数据归属ID（同user_id）"
        data_owner_type VARCHAR(20) NN "数据归属类型：user"
        my_id VARCHAR(50) UK "澳大利亚MyID（唯一）"
        kyc_data JSON NN "KYC详细数据（JSON）"
        kyc_status VARCHAR(20) NN "pending_review/approved/rejected/expired"
        kyc_submit_time DATETIME NN "KYC提交时间"
        kyc_expire_time DATETIME NN "KYC过期时间"
        kyc_approved_time DATETIME "" "KYC批准时间"
        reviewer_id BIGINT FK "关联saas_platform_admin.admin_id（审核人）"
        review_notes VARCHAR(500) "" "审核备注"
        create_time DATETIME NN "创建时间"
        update_time DATETIME NN "更新时间"
    }

    saas_enterprise_customer {
        enterprise_id BIGINT PK "企业客户唯一ID"
        enterprise_name VARCHAR(100) NN "企业名称"
        business_number VARCHAR(50) UK "商业登记号（唯一）"
        ran VARCHAR(50) "" "Registered Agent Number"
        enterprise_status VARCHAR(20) NN "active/inactive/cancelled"
        kyb_status VARCHAR(20) NN "pending_review/approved/rejected/expired"
        authorized_user_id BIGINT FK "关联saas_user.user_id（授权代表）"
        data_owner_id BIGINT NN "数据归属ID（同enterprise_id）"
        data_owner_type VARCHAR(20) NN "数据归属类型：enterprise"
        kyb_submit_time DATETIME NN "KYB提交时间"
        kyb_expire_time DATETIME NN "KYB过期时间"
        enterprise_address VARCHAR(255) "" "企业地址"
        contact_phone VARCHAR(20) "" "联系电话"
        create_time DATETIME NN "创建时间"
        update_time DATETIME NN "更新时间"
    }

    saas_enterprise_kyb {
        kyb_id BIGINT PK "KYB记录唯一ID"
        enterprise_id BIGINT FK "关联saas_enterprise_customer.enterprise_id"
        data_owner_id BIGINT NN "数据归属ID（同enterprise_id）"
        data_owner_type VARCHAR(20) NN "数据归属类型：enterprise"
        kyb_data JSON NN "KYB详细数据（JSON）"
        kyb_approved_time DATETIME "" "KYB批准时间"
        reviewer_id BIGINT FK "关联saas_platform_admin.admin_id（审核人）"
        review_notes VARCHAR(500) "" "审核备注"
        create_time DATETIME NN "创建时间"
        update_time DATETIME NN "更新时间"
    }

    %% ===================== 4. 客户授权模块 =====================
    saas_individual_auth {
        auth_id BIGINT PK "授权记录唯一ID"
        user_id BIGINT FK "关联saas_user.user_id（授权方）"
        tenant_id BIGINT FK "关联saas_tenant.tenant_id（被授权方）"
        data_owner_id BIGINT NN "数据归属ID（同tenant_id，租户数据权限用）"
        data_owner_type VARCHAR(20) NN "数据归属类型：tenant"
        auth_business_type VARCHAR(50) NN "授权业务类型（自包含）"
        auth_status VARCHAR(20) NN "pending_signature/signed/authorized/expired/cancelled"
        docusign_envelope_id VARCHAR(100) "" "DocuSign信封ID"
        auth_effective_time DATETIME NN "授权生效时间"
        auth_expire_time DATETIME NN "授权过期时间"
        ato_agent_add_status VARCHAR(20) NN "pending/completed/failed"
        reminder_days INT NN "提醒天数"
        reminder_sent TINYINT(1) NN "是否已发送提醒"
        create_time DATETIME NN "创建时间"
        update_time DATETIME NN "更新时间"
    }

    saas_enterprise_auth {
        auth_id BIGINT PK "授权记录唯一ID"
        enterprise_id BIGINT FK "关联saas_enterprise_customer.enterprise_id（授权方）"
        tenant_id BIGINT FK "关联saas_tenant.tenant_id（被授权方）"
        data_owner_id BIGINT NN "数据归属ID（同tenant_id）"
        data_owner_type VARCHAR(20) NN "数据归属类型：tenant"
        auth_business_type VARCHAR(50) NN "授权业务类型（自包含）"
        auth_status VARCHAR(20) NN "pending_signature/signed/authorized/expired/cancelled"
        docusign_envelope_id VARCHAR(100) "" "DocuSign信封ID"
        auth_effective_time DATETIME NN "授权生效时间"
        auth_expire_time DATETIME NN "授权过期时间"
        ato_nomination_status VARCHAR(20) NN "pending/completed/failed"
        reminder_days INT NN "提醒天数"
        reminder_sent TINYINT(1) NN "是否已发送提醒"
        create_time DATETIME NN "创建时间"
        update_time DATETIME NN "更新时间"
    }

    %% ===================== 5. 安全验证模块（OTP） =====================
    saas_otp_record {
        otp_id BIGINT PK "OTP记录唯一ID"
        target_id BIGINT NN "关联user_id/tenant_user_id"
        target_type VARCHAR(20) NN "user/tenant_user"
        data_owner_id BIGINT NN "数据归属ID（同target_id）"
        data_owner_type VARCHAR(20) NN "数据归属类型（同target_type）"
        otp_type VARCHAR(20) NN "first_login/password_reset"
        otp_code VARCHAR(64) NN "加密OTP码"
        otp_expire_time DATETIME NN "OTP过期时间"
        verify_status VARCHAR(20) NN "unverified/verified/expired"
        send_time DATETIME NN "发送时间"
        verify_time DATETIME "" "验证时间"
        create_time DATETIME NN "创建时间"
    }

    %% ===================== 6. 数据权限模块 =====================
    saas_data_permission {
        data_perm_id BIGINT PK "数据权限唯一ID"
        data_perm_name VARCHAR(50) UK "数据权限名称（唯一）"
        data_perm_type VARCHAR(20) NN "tenant_scope/customer_scope/platform_scope"
        default_data_scope VARCHAR(50) NN "默认数据范围：如tenant_id = #{currentTenantId}"
        data_perm_desc VARCHAR(200) NN "权限描述"
        create_time DATETIME NN "创建时间"
        update_time DATETIME NN "更新时间"
    }

    saas_role_data_perm_rel {
        rel_id BIGINT PK "关联记录唯一ID"
        role_id INT FK "关联saas_role.role_id"
        data_perm_id BIGINT FK "关联saas_data_permission.data_perm_id"
        tenant_id BIGINT NN "租户ID：平台级权限填0，租户级填实际tenant_id"
        data_owner_id BIGINT NN "数据归属ID（同tenant_id）"
        data_owner_type VARCHAR(20) NN "数据归属类型：tenant"
        create_time DATETIME NN "创建时间"
    }

    saas_role_customer_scope {
        scope_id BIGINT PK "范围记录唯一ID"
        role_id INT FK "关联saas_role.role_id"
        tenant_id BIGINT NN "关联saas_tenant.tenant_id（所属租户）"
        user_id BIGINT FK "关联saas_user.user_id（个人客户，二选一）"
        enterprise_id BIGINT FK "关联saas_enterprise_customer.enterprise_id（企业客户，二选一）"
        data_owner_id BIGINT NN "数据归属ID（tenant_id，租户级数据权限）"
        data_owner_type VARCHAR(20) NN "数据归属类型：tenant"
        create_time DATETIME NN "创建时间"
        expire_time DATETIME "" "过期时间"
    }

    %% ===================== 7. 前端权限模块（菜单/控件） =====================
    saas_frontend_menu {
        menu_id BIGINT PK "菜单唯一ID"
        menu_name VARCHAR(50) NN "菜单名称（如租户管理客户KYC）"
        parent_menu_id BIGINT NN "父菜单ID：0=一级菜单，>0=子菜单"
        menu_url VARCHAR(255) UK "前端路由地址（如/tenant/list）"
        menu_icon VARCHAR(50) "" "菜单图标（如el-icon-s-tools）"
        menu_sort INT NN "菜单排序号（数字越小越靠前）"
        menu_type VARCHAR(20) NN "菜单类型：directory（目录）/menu（菜单）/button（按钮，特殊菜单）"
        visible_status TINYINT(1) NN "是否默认显示：1=显示，0=隐藏（需权限控制）"
        data_owner_type VARCHAR(20) NN "数据归属类型：platform/tenant"
        data_owner_id BIGINT NN "数据归属ID：平台级=0，租户级=tenant_id"
        create_time DATETIME NN "创建时间"
        update_time DATETIME NN "更新时间"
    }

    saas_role_frontend_menu_rel {
        rel_id BIGINT PK "关联记录唯一ID"
        role_id INT FK "关联saas_role.role_id（角色ID）"
        menu_id BIGINT FK "关联saas_frontend_menu.menu_id（菜单ID）"
        permission_id INT FK "关联saas_permission.permission_id（后端功能权限ID，确保前后端权限一致）"
        tenant_id BIGINT NN "租户ID：平台级角色=0，租户级角色=实际tenant_id"
        data_owner_id BIGINT NN "数据归属ID（同tenant_id）"
        data_owner_type VARCHAR(20) NN "数据归属类型：tenant"
        create_time DATETIME NN "创建时间"
    }

    saas_frontend_control {
        control_id BIGINT PK "控件唯一ID"
        control_name VARCHAR(50) NN "控件名称（如新增租户按钮审核KYC开关）"
        menu_id BIGINT FK "关联saas_frontend_menu.menu_id（所属菜单，必选）"
        control_selector VARCHAR(100) UK "前端控件唯一标识（如tenant-add-btn kyc-review-switch）"
        control_type VARCHAR(20) NN "控件类型：button（按钮）/switch（开关）/input（输入框）/select（下拉框）"
        control_action VARCHAR(50) "" "控件触发动作（如add delete review）"
        default_status TINYINT(1) NN "默认状态：0=禁用/隐藏，1=启用/显示（需权限控制）"
        data_owner_type VARCHAR(20) NN "数据归属类型：platform/tenant"
        data_owner_id BIGINT NN "数据归属ID：平台级=0，租户级=tenant_id"
        create_time DATETIME NN "创建时间"
        update_time DATETIME NN "更新时间"
    }

    saas_role_frontend_control_rel {
        rel_id BIGINT PK "关联记录唯一ID"
        role_id INT FK "关联saas_role.role_id（角色ID）"
        control_id BIGINT FK "关联saas_frontend_control.control_id（控件ID）"
        permission_id INT NN "关联saas_permission.permission_id（后端功能权限ID，必关联，确保前后端一致）"
        tenant_id BIGINT NN "租户ID：平台级角色=0，租户级角色=实际tenant_id"
        control_status TINYINT(1) NN "控件权限状态：1=启用/显示，0=禁用/隐藏"
        data_owner_id BIGINT NN "数据归属ID（同tenant_id）"
        data_owner_type VARCHAR(20) NN "数据归属类型：tenant"
        create_time DATETIME NN "创建时间"
    }

    %% ===================== 实体间关系定义 =====================
    %% 1. 平台基础模块关系
    saas_role }|--|{ saas_permission : "拥有功能权限（N→M）" via saas_role_permission
    saas_platform_admin }|--|| saas_role : "关联角色（N→1）"

    %% 2. 租户模块关系
    saas_tenant }|--|{ saas_tenant_user : "包含IAM用户（1→N）"
    saas_tenant_user }|--|| saas_role : "关联租户角色（N→1）"

    %% 3. 用户与客户模块关系
    saas_tenant }|--|{ saas_tenant_customer_invite : "发起客户邀请（1→N）"
    saas_tenant_customer_invite }|--|| saas_user : "关联被邀请用户（N→1）"
    saas_user }|--|| saas_individual_kyc : "完成个人KYC（1→1）"
    saas_user }|--|{ saas_enterprise_customer : "授权代表企业（1→N）"
    saas_enterprise_customer }|--|| saas_enterprise_kyb : "完成企业KYB（1→1）"
    saas_platform_admin }|--|{ saas_individual_kyc : "审核个人KYC（1→N）"
    saas_platform_admin }|--|{ saas_enterprise_kyb : "审核企业KYB（1→N）"

    %% 4. 客户授权模块关系
    saas_user }|--|{ saas_individual_auth : "个人授权租户（1→N）"
    saas_tenant }|--|{ saas_individual_auth : "接收个人授权（1→N）"
    saas_enterprise_customer }|--|{ saas_enterprise_auth : "企业授权租户（1→N）"
    saas_tenant }|--|{ saas_enterprise_auth : "接收企业授权（1→N）"

    %% 5. 安全验证模块关系
    saas_user }|--|{ saas_otp_record : "生成用户OTP（1→N）"
    saas_tenant_user }|--|{ saas_otp_record : "生成IAM用户OTP（1→N）"

    %% 6. 数据权限模块关系
    saas_role }|--|{ saas_data_permission : "拥有数据权限（N→M）" via saas_role_data_perm_rel
    saas_tenant }|--|{ saas_role_data_perm_rel : "绑定租户数据权限（1→N）"
    saas_role }|--|{ saas_role_customer_scope : "关联指定客户范围（1→N）"
    saas_tenant }|--|{ saas_role_customer_scope : "限定客户范围租户（1→N）"
    saas_user }|--|{ saas_role_customer_scope : "包含个人客户（1→N）"
    saas_enterprise_customer }|--|{ saas_role_customer_scope : "包含企业客户（1→N）"

    %% 7. 前端权限模块关系
    saas_role }|--|{ saas_frontend_menu : "拥有菜单权限（N→M）" via saas_role_frontend_menu_rel
    saas_frontend_menu }|--|{ saas_frontend_control : "包含页面控件（1→N）"
    saas_role }|--|{ saas_frontend_control : "拥有控件权限（N→M）" via saas_role_frontend_control_rel
    saas_permission }|--|{ saas_role_frontend_control_rel : "关联后端功能权限（1→N）"