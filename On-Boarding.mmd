erDiagram
    %% ===================== 1. 平台基础模块（RBAC核心） =====================
    saas_role {
        INT role_id PK
        VARCHAR role_name UK
        VARCHAR role_type "NOT NULL"
        VARCHAR role_description
        VARCHAR data_scope "NOT NULL"
        DATETIME create_time "NOT NULL"
        DATETIME update_time "NOT NULL"
    }

    saas_permission {
        INT permission_id PK
        VARCHAR permission_name UK
        VARCHAR permission_desc
        VARCHAR permission_type "NOT NULL"
        TINYINT data_perm_flag "NOT NULL"
        DATETIME create_time "NOT NULL"
        DATETIME update_time "NOT NULL"
    }

    saas_role_permission {
        BIGINT id PK
        INT role_id FK
        INT permission_id FK
        DATETIME create_time "NOT NULL"
    }

    saas_platform_admin {
        BIGINT admin_id PK
        VARCHAR admin_account UK
        VARCHAR login_password "NOT NULL"
        VARCHAR admin_name "NOT NULL"
        INT admin_role_id FK
        VARCHAR data_scope "NOT NULL"
        VARCHAR admin_status "NOT NULL"
        DATETIME last_login_time
        DATETIME create_time "NOT NULL"
        DATETIME update_time "NOT NULL"
    }

    %% ===================== 2. 租户模块（会计师事务所） =====================
    saas_tenant {
        BIGINT tenant_id PK
        VARCHAR tenant_name "NOT NULL"
        VARCHAR business_number UK
        VARCHAR ran UK
        VARCHAR onboarding_status "NOT NULL"
        VARCHAR tenant_status "NOT NULL"
        VARCHAR contact_email "NOT NULL"
        VARCHAR contact_phone
        DATETIME tenant_create_time "NOT NULL"
        DATETIME tenant_activate_time
        DATETIME update_time "NOT NULL"
    }

    saas_tenant_user {
        BIGINT tenant_user_id PK
        BIGINT tenant_id FK
        BIGINT data_owner_id "NOT NULL"
        VARCHAR data_owner_type "NOT NULL"
        VARCHAR login_email UK
        VARCHAR initial_password "NOT NULL"
        TINYINT password_changed "NOT NULL"
        VARCHAR tenant_user_role FK
        VARCHAR staff_name
        VARCHAR staff_title
        VARCHAR staff_phone
        TINYINT first_login_status "NOT NULL"
        TINYINT otp_verified "NOT NULL"
        VARCHAR user_status "NOT NULL"
        DATETIME create_time "NOT NULL"
        DATETIME update_time "NOT NULL"
    }

    %% ===================== 3. 用户与客户模块 =====================
    saas_user {
        BIGINT user_id PK
        VARCHAR username UK
        VARCHAR login_password "NOT NULL"
        VARCHAR email UK
        TINYINT email_verified "NOT NULL"
        VARCHAR user_type "NOT NULL"
        VARCHAR registration_method "NOT NULL"
        VARCHAR data_scope "NOT NULL"
        VARCHAR user_status "NOT NULL"
        DATETIME register_time "NOT NULL"
        DATETIME last_login_time
        DATETIME create_time "NOT NULL"
        DATETIME update_time "NOT NULL"
    }

    saas_tenant_customer_invite {
        BIGINT invite_id PK
        BIGINT tenant_id FK
        BIGINT data_owner_id "NOT NULL"
        VARCHAR data_owner_type "NOT NULL"
        VARCHAR invited_email "NOT NULL"
        VARCHAR invite_type "NOT NULL"
        VARCHAR invite_status "NOT NULL"
        VARCHAR invite_link UK
        DATETIME link_expire_time "NOT NULL"
        DATETIME invite_send_time "NOT NULL"
        DATETIME accept_time
        DATETIME create_time "NOT NULL"
        DATETIME update_time "NOT NULL"
    }

    saas_individual_kyc {
        BIGINT kyc_id PK
        BIGINT user_id FK
        BIGINT data_owner_id "NOT NULL"
        VARCHAR data_owner_type "NOT NULL"
        VARCHAR my_id UK
        JSON kyc_data "NOT NULL"
        VARCHAR kyc_status "NOT NULL"
        DATETIME kyc_submit_time "NOT NULL"
        DATETIME kyc_expire_time "NOT NULL"
        DATETIME kyc_approved_time
        BIGINT reviewer_id FK
        VARCHAR review_notes
        DATETIME create_time "NOT NULL"
        DATETIME update_time "NOT NULL"
    }

    saas_enterprise_customer {
        BIGINT enterprise_id PK
        VARCHAR enterprise_name "NOT NULL"
        VARCHAR business_number UK
        VARCHAR ran
        VARCHAR enterprise_status "NOT NULL"
        VARCHAR kyb_status "NOT NULL"
        BIGINT authorized_user_id FK
        BIGINT data_owner_id "NOT NULL"
        VARCHAR data_owner_type "NOT NULL"
        DATETIME kyb_submit_time "NOT NULL"
        DATETIME kyb_expire_time "NOT NULL"
        VARCHAR enterprise_address
        VARCHAR contact_phone
        DATETIME create_time "NOT NULL"
        DATETIME update_time "NOT NULL"
    }

    saas_enterprise_kyb {
        BIGINT kyb_id PK
        BIGINT enterprise_id FK
        BIGINT data_owner_id "NOT NULL"
        VARCHAR data_owner_type "NOT NULL"
        JSON kyb_data "NOT NULL"
        DATETIME kyb_approved_time
        BIGINT reviewer_id FK
        VARCHAR review_notes
        DATETIME create_time "NOT NULL"
        DATETIME update_time "NOT NULL"
    }

    %% ===================== 4. 客户授权模块 =====================
    saas_individual_auth {
        BIGINT auth_id PK
        BIGINT user_id FK
        BIGINT tenant_id FK
        BIGINT data_owner_id "NOT NULL"
        VARCHAR data_owner_type "NOT NULL"
        VARCHAR auth_business_type "NOT NULL"
        VARCHAR auth_status "NOT NULL"
        VARCHAR docusign_envelope_id
        DATETIME auth_effective_time "NOT NULL"
        DATETIME auth_expire_time "NOT NULL"
        VARCHAR ato_agent_add_status "NOT NULL"
        INT reminder_days "NOT NULL"
        TINYINT reminder_sent "NOT NULL"
        DATETIME create_time "NOT NULL"
        DATETIME update_time "NOT NULL"
    }

    saas_enterprise_auth {
        BIGINT auth_id PK
        BIGINT enterprise_id FK
        BIGINT tenant_id FK
        BIGINT data_owner_id "NOT NULL"
        VARCHAR data_owner_type "NOT NULL"
        VARCHAR auth_business_type "NOT NULL"
        VARCHAR auth_status "NOT NULL"
        VARCHAR docusign_envelope_id
        DATETIME auth_effective_time "NOT NULL"
        DATETIME auth_expire_time "NOT NULL"
        VARCHAR ato_nomination_status "NOT NULL"
        INT reminder_days "NOT NULL"
        TINYINT reminder_sent "NOT NULL"
        DATETIME create_time "NOT NULL"
        DATETIME update_time "NOT NULL"
    }

    %% ===================== 5. 安全验证模块（OTP） =====================
    saas_otp_record {
        BIGINT otp_id PK
        BIGINT target_id "NOT NULL"
        VARCHAR target_type "NOT NULL"
        BIGINT data_owner_id "NOT NULL"
        VARCHAR data_owner_type "NOT NULL"
        VARCHAR otp_type "NOT NULL"
        VARCHAR otp_code "NOT NULL"
        DATETIME otp_expire_time "NOT NULL"
        VARCHAR verify_status "NOT NULL"
        DATETIME send_time "NOT NULL"
        DATETIME verify_time
        DATETIME create_time "NOT NULL"
    }

    %% ===================== 6. 数据权限模块 =====================
    saas_data_permission {
        BIGINT data_perm_id PK
        VARCHAR data_perm_name UK
        VARCHAR data_perm_type "NOT NULL"
        VARCHAR default_data_scope "NOT NULL"
        VARCHAR data_perm_desc "NOT NULL"
        DATETIME create_time "NOT NULL"
        DATETIME update_time "NOT NULL"
    }

    saas_role_data_perm_rel {
        BIGINT rel_id PK
        INT role_id FK
        BIGINT data_perm_id FK
        BIGINT tenant_id "NOT NULL"
        BIGINT data_owner_id "NOT NULL"
        VARCHAR data_owner_type "NOT NULL"
        DATETIME create_time "NOT NULL"
    }

    saas_role_customer_scope {
        BIGINT scope_id PK
        INT role_id FK
        BIGINT tenant_id "NOT NULL"
        BIGINT user_id FK
        BIGINT enterprise_id FK
        BIGINT data_owner_id "NOT NULL"
        VARCHAR data_owner_type "NOT NULL"
        DATETIME create_time "NOT NULL"
        DATETIME expire_time
    }

    %% ===================== 7. 前端权限模块（菜单/控件） =====================
    saas_frontend_menu {
        BIGINT menu_id PK
        VARCHAR menu_name "NOT NULL"
        BIGINT parent_menu_id "NOT NULL"
        VARCHAR menu_url UK
        VARCHAR menu_icon
        INT menu_sort "NOT NULL"
        VARCHAR menu_type "NOT NULL"
        TINYINT visible_status "NOT NULL"
        VARCHAR data_owner_type "NOT NULL"
        BIGINT data_owner_id "NOT NULL"
        DATETIME create_time "NOT NULL"
        DATETIME update_time "NOT NULL"
    }

    saas_role_frontend_menu_rel {
        BIGINT rel_id PK
        INT role_id FK
        BIGINT menu_id FK
        INT permission_id FK
        BIGINT tenant_id "NOT NULL"
        BIGINT data_owner_id "NOT NULL"
        VARCHAR data_owner_type "NOT NULL"
        DATETIME create_time "NOT NULL"
    }

    saas_frontend_control {
        BIGINT control_id PK
        VARCHAR control_name "NOT NULL"
        BIGINT menu_id FK
        VARCHAR control_selector UK
        VARCHAR control_type "NOT NULL"
        VARCHAR control_action
        TINYINT default_status "NOT NULL"
        VARCHAR data_owner_type "NOT NULL"
        BIGINT data_owner_id "NOT NULL"
        DATETIME create_time "NOT NULL"
        DATETIME update_time "NOT NULL"
    }

    saas_role_frontend_control_rel {
        BIGINT rel_id PK
        INT role_id FK
        BIGINT control_id FK
        INT permission_id "NOT NULL"
        BIGINT tenant_id "NOT NULL"
        TINYINT control_status "NOT NULL"
        BIGINT data_owner_id "NOT NULL"
        VARCHAR data_owner_type "NOT NULL"
        DATETIME create_time "NOT NULL"
    }

    %% ===================== 实体间关系定义 =====================
    %% 1. 平台基础模块关系
    saas_role }|--|{ saas_permission : "拥有功能权限（N→M）" via saas_role_permission
    saas_platform_admin }|--|| saas_role : "关联角色（N→1）"

    %% 2. 租户模块关系
    saas_tenant }|--|{ saas_tenant_user : "包含IAM用户（1→N）"
    saas_tenant_user }|--|| saas_role : "关联租户角色（N→1）"

    %% 3. 用户与客户模块关系
    saas_tenant }|--|{ saas_tenant_customer_invite : "发起客户邀请（1→N）"
    saas_tenant_customer_invite }|--|| saas_user : "关联被邀请用户（N→1）"
    saas_user }|--|| saas_individual_kyc : "完成个人KYC（1→1）"
    saas_user }|--|{ saas_enterprise_customer : "授权代表企业（1→N）"
    saas_enterprise_customer }|--|| saas_enterprise_kyb : "完成企业KYB（1→1）"
    saas_platform_admin }|--|{ saas_individual_kyc : "审核个人KYC（1→N）"
    saas_platform_admin }|--|{ saas_enterprise_kyb : "审核企业KYB（1→N）"

    %% 4. 客户授权模块关系
    saas_user }|--|{ saas_individual_auth : "个人授权租户（1→N）"
    saas_tenant }|--|{ saas_individual_auth : "接收个人授权（1→N）"
    saas_enterprise_customer }|--|{ saas_enterprise_auth : "企业授权租户（1→N）"
    saas_tenant }|--|{ saas_enterprise_auth : "接收企业授权（1→N）"

    %% 5. 安全验证模块关系
    saas_user }|--|{ saas_otp_record : "生成用户OTP（1→N）"
    saas_tenant_user }|--|{ saas_otp_record : "生成IAM用户OTP（1→N）"

    %% 6. 数据权限模块关系
    saas_role }|--|{ saas_data_permission : "拥有数据权限（N→M）" via saas_role_data_perm_rel
    saas_tenant }|--|{ saas_role_data_perm_rel : "绑定租户数据权限（1→N）"
    saas_role }|--|{ saas_role_customer_scope : "关联指定客户范围（1→N）"
    saas_tenant }|--|{ saas_role_customer_scope : "限定客户范围租户（1→N）"
    saas_user }|--|{ saas_role_customer_scope : "包含个人客户（1→N）"
    saas_enterprise_customer }|--|{ saas_role_customer_scope : "包含企业客户（1→N）"

    %% 7. 前端权限模块关系
    saas_role }|--|{ saas_frontend_menu : "拥有菜单权限（N→M）" via saas_role_frontend_menu_rel
    saas_frontend_menu }|--|{ saas_frontend_control : "包含页面控件（1→N）"
    saas_role }|--|{ saas_frontend_control : "拥有控件权限（N→M）" via saas_role_frontend_control_rel
    saas_permission }|--|{ saas_role_frontend_control_rel : "关联后端功能权限（1→N）"